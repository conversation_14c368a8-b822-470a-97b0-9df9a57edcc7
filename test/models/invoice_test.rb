# == Schema Information
#
# Table name: invoices
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  charge_id          :bigint           not null
#  idoklad_invoice_id :integer
#  user_id            :integer
#
# Indexes
#
#  index_invoices_on_charge_id  (charge_id)
#
# Foreign Keys
#
#  fk_rails_...  (charge_id => pay_charges.id)
#
require "test_helper"

class InvoiceTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
