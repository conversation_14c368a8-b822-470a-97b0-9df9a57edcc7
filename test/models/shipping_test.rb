# frozen_string_literal: true

# == Schema Information
#
# Table name: shippings
#
#  id         :bigint           not null, primary key
#  country    :string
#  free_from  :integer
#  is_pickup  :boolean          default(FALSE)
#  name       :string
#  price      :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
require "test_helper"

class ShippingTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
