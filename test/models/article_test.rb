# frozen_string_literal: true

# == Schema Information
#
# Table name: articles
#
#  id             :bigint           not null, primary key
#  backup_content :text
#  content        :text
#  display_ads    :boolean          default(TRUE)
#  other_authors  :string
#  perex          :string
#  published_at   :datetime
#  slug           :string
#  species        :string
#  title          :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  author_id      :bigint           not null
#  magazine_id    :bigint
#  original_id    :integer
#
# Indexes
#
#  index_articles_on_author_id    (author_id)
#  index_articles_on_magazine_id  (magazine_id)
#  index_articles_on_slug         (slug) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (author_id => authors.id)
#  fk_rails_...  (magazine_id => magazines.id)
#
require "test_helper"

class ArticleTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
