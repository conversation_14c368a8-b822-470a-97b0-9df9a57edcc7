// Tento skript se pokusí opravit problém s neuzavřenými komentáři v admin.js
const fs = require('fs');
const path = require('path');

// Cesta k minifikovanému souboru
const adminJsPath = path.join(__dirname, 'app/assets/builds/admin.js');

// Načtení obsahu souboru
let content = fs.readFileSync(adminJsPath, 'utf8');

// Vytvoření kopie souboru pro zálohu
fs.writeFileSync(adminJsPath + '.bak', content);
console.log(`Vytvořena záloha souboru jako ${adminJsPath}.bak`);

// Nahrazení problematických řetězců
content = content.replace(/Press enter to add a show text/g, 'Press enter to add a new text');
content = content.replace(/Open link in show tab/g, 'Open link in new tab');
content = content.replace(/Drag to upload a show image/g, 'Drag to upload a new image');

// Uložení opraveného souboru
fs.writeFileSync(adminJsPath, content);
console.log(`Soubor ${adminJsPath} byl opraven.`);

// Kontrola na výskyt řetězce "show text"
if (content.includes('show text')) {
    console.log('VAROVÁNÍ: Stále nalezen řetězec "show text"!');
} else {
    console.log('Řetězec "show text" byl úspěšně nahrazen.');
}

// Kontrola na výskyt řetězce "show tab"
if (content.includes('show tab')) {
    console.log('VAROVÁNÍ: Stále nalezen řetězec "show tab"!');
} else {
    console.log('Řetězec "show tab" byl úspěšně nahrazen.');
}

// Kontrola na výskyt řetězce "show image"
if (content.includes('show image')) {
    console.log('VAROVÁNÍ: Stále nalezen řetězec "show image"!');
} else {
    console.log('Řetězec "show image" byl úspěšně nahrazen.');
}
