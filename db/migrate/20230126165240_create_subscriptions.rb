# frozen_string_literal: true

class CreateSubscriptions < ActiveRecord::Migration[7.0]
  def change
    create_table :subscriptions do |t|
      t.string :email
      t.string :phone_number
      t.string :first_name
      t.string :surname
      t.string :address
      t.string :city
      t.integer :zip
      t.string :from_issue, default: nil
      t.integer :plan, default: 0
      t.belongs_to :user, null: false, foreign_key: true

      t.timestamps
    end
  end
end
