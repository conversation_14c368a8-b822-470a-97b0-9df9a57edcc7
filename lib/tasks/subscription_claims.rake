namespace :subscription_claims do
  desc "Import subscription claims from /lib/subscription_claims.csv file"
  task import: :environment do
    require 'csv'

    # Použi<PERSON><PERSON> pevně dané cesty k souboru v /lib
    file_path = Rails.root.join('lib', 'subscription_claims.csv')

    unless File.exist?(file_path)
      puts "Error: File not found at #{file_path}"
      puts "Please make sure the file exists at #{file_path}"
      exit 1
    end

    puts "Importing subscription claims from #{file_path}..."

    # Poznámka: Před importem je vhodné resetovat sekvenci ID v databázi pomocí SQL dotazu

    count = 0
    CSV.foreach(file_path, headers: false) do |row|
      email = row[0]&.strip&.gsub(/^"|"$/, '') # Odstraní uvozovky a bílé znaky
      full_name = row[1]&.strip&.gsub(/^"|"$/, '') # Odstraní uvozovky a bí<PERSON> znaky

      next if email.blank? || full_name.blank?

      # Rozdělení celého jména na jméno a příjmení
      # Předpokl<PERSON>dáme, že formát je "Příjmení Jméno"
      name_parts = full_name.split(' ')
      if name_parts.length >= 2
        # Podle dat v CSV je formát "Příjmení Jméno"
        surname = name_parts[0]
        first_name = name_parts[1..-1].join(' ')
      else
        # Pokud je jen jedno slovo, použijeme ho jako příjmení
        surname = full_name
        first_name = ""
      end

      # Skip if already exists
      if SubscriptionClaim.find_by(email: email)
        puts "Skipping duplicate email: #{email}"
        next
      end

      # Generování unikátního tokenu
      token = SecureRandom.hex(16)
      while SubscriptionClaim.exists?(token: token)
        token = SecureRandom.hex(16)
      end

      claim = SubscriptionClaim.create!(
        email: email,
        first_name: first_name,
        surname: surname,
        token: token
      )

      count += 1
      puts "Created claim for #{email} (#{first_name} #{surname})"
    end

    puts "Import completed. Created #{count} subscription claims."
  end

  desc "Schedule emails for all unscheduled subscription claims"
  task schedule_emails: :environment do
    claims = SubscriptionClaim.not_sent.not_claimed
    scheduled_time = Time.current

    claims.each do |claim|
      claim.schedule_email!(scheduled_time)
      puts "Scheduled email for #{claim.email}"
    end

    puts "Scheduled #{claims.count} emails."
  end

  desc "Send emails to all scheduled subscription claims"
  task send_emails: :environment do
    claims = SubscriptionClaim.scheduled_to_send.not_claimed

    claims.each do |claim|
      SubscriptionClaimMailer.claim_email(claim).deliver_now
      claim.mark_as_sent!
      puts "Sent email to #{claim.email}"
    end

    puts "Sent #{claims.count} emails."
  end

  desc "List all subscription claims that are scheduled to be sent but not yet sent or claimed"
  task list_pending: :environment do
    claims = SubscriptionClaim.where(claimed_at: nil, email_sent_at: nil)
                             .where('email_scheduled_at <= ?', Time.current)

    if claims.empty?
      puts "No pending subscription claims found."
    else
      puts "Found #{claims.count} pending subscription claims:"
      puts "ID\tEmail\t\t\tFirst Name\tLast Name\tScheduled At"
      puts "-" * 80

      claims.each do |claim|
        puts "#{claim.id}\t#{claim.email.ljust(20)}\t#{claim.first_name.ljust(12)}\t#{claim.surname.ljust(12)}\t#{claim.email_scheduled_at}"
      end
    end
  end

  desc "Automatically process and send emails for scheduled subscription claims (suitable for cron)"
  task process_scheduled: :environment do
    # Najde všechny záznamy, které jsou naplánovány k odeslání a ještě nebyly vyzvednuty nebo odeslány
    claims = SubscriptionClaim.where(claimed_at: nil, email_sent_at: nil)
                             .where('email_scheduled_at <= ?', Time.current)
                             .order(email_scheduled_at: :asc)
                             .limit(50)  # Omezení počtu odesílaných emailů v jednom běhu

    if claims.empty?
      puts "[#{Time.current}] No pending subscription claims to process."
    else
      puts "[#{Time.current}] Processing #{claims.count} pending subscription claims..."

      success_count = 0
      error_count = 0

      claims.each do |claim|
        begin
          # Odeslání emailu
          SubscriptionClaimMailer.claim_email(claim).deliver_now

          # Označení jako odesláno
          claim.mark_as_sent!

          success_count += 1
          puts "[#{Time.current}] Successfully sent email to #{claim.email} (ID: #{claim.id})"
        rescue => e
          error_count += 1
          puts "[#{Time.current}] ERROR sending email to #{claim.email} (ID: #{claim.id}): #{e.message}"
        end
      end

      puts "[#{Time.current}] Completed processing: #{success_count} emails sent successfully, #{error_count} errors."
    end
  end
end
