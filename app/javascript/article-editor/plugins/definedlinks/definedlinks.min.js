ArticleEditor.add("plugin","definedlinks",{defaults:{items:!1},subscribe:{"popup.open":function(){var t,e;!1!==this.opts.definedlinks.items&&(e=(t=this.app.popup.getStack()).getName(),-1!==["link-create","link-edit"].indexOf(e))&&this._build(t)}},_build:function(t){var t=(this.stack=t).getFormItem("text"),e=this.dom("<div>").addClass(this.prefix+"-form-item");this.$select=this._create(),e.append(this.$select),t.before(e)},_change:function(t){var t=this.dom(t.target).val(),t=this.opts.definedlinks.items[t],e=this.stack.getInput("text"),i=this.stack.getInput("url"),s=t.name,n=t.url;!1===t.url&&(s=n=""),e.val(s),i.val(n)},_create:function(){var t=this.opts.definedlinks.items,e=this.dom("<select>").addClass(this.prefix+"-form-select");e.on("change",this._change.bind(this));for(var i=0;i<t.length;i++){var s=t[i],n=this.dom("<option>");n.val(i),n.html(s.name),e.append(n)}return e}});