ArticleEditor.add("plugin","buttonlink",{translations:{en:{buttonlink:{button:"Button"}}},defaults:{classname:!1},subscribe:{"popup.before.open":function(){var t;!1!==this.opts.buttonlink.classname&&"link"===(t=this.app.popup.getStack()).getName()&&this._build(t)}},make:function(){this.app.popup.close(),this.app.link.getLink().addClass(this.opts.buttonlink.classname)},unmake:function(){this.app.popup.close(),this.app.link.getLink().removeClass(this.opts.buttonlink.classname)},_build:function(t){var n,s,i,a=this.app.link.getLink();0!==a.length&&(n=t.getItemsData(),s={title:"## buttonlink.button ##",command:"buttonlink.make"},i=this.app.utils.getObjectIndex(n,"unlink"),this._hasClass(a)&&(s={title:"## buttonlink.button ##",command:"buttonlink.unmake",active:!0}),n=this.app.utils.insertToObject("buttonlink",s,n,i),t.setItemsData(n))},_hasClass:function(t){for(var n=this.opts.buttonlink.classname.split(" "),s=0,i=0;i<n.length;i++)t.hasClass(n[i])&&s++;return s===n.length}});