import { Controller } from "@hotwired/stimulus"

// Univerzální controller pro přepínání mezi skrytým a zobrazeným obsahem
export default class extends Controller {
  static targets = ["collapsed", "expanded"]
  static values = {
    group: String
  }

  connect() {
    // Inicializace controlleru
    console.log("Toggle controller connected")
  }

  // Zobrazí rozšířený obsah a skryje zkolabovaný
  show(event) {
    // Získáme skupinu, ke které pat<PERSON> (pokud existuje)
    const group = event.currentTarget.dataset.group || this.groupValue || ''
    
    // Najdeme odpovídající elementy podle skupiny
    this.collapsedTargets.forEach(element => {
      if (element.dataset.group === group) {
        element.classList.add("hidden")
      }
    })
    
    this.expandedTargets.forEach(element => {
      if (element.dataset.group === group) {
        element.classList.remove("hidden")
      }
    })
  }

  // Skryje r<PERSON> obsah a zobraz<PERSON> zkolabo<PERSON>
  hide(event) {
    // <PERSON>ísk<PERSON><PERSON> skupin<PERSON>, ke které pat<PERSON> (pokud existuje)
    const group = event.currentTarget.dataset.group || this.groupValue || ''
    
    // Najdeme odpovídající elementy podle skupiny
    this.expandedTargets.forEach(element => {
      if (element.dataset.group === group) {
        element.classList.add("hidden")
      }
    })
    
    this.collapsedTargets.forEach(element => {
      if (element.dataset.group === group) {
        element.classList.remove("hidden")
      }
    })
  }
}
