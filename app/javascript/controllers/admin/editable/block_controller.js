import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static values = { container: String, themeValue: String, mediaAlignment: String, mediaContainer: String };

    connect() {
        document.getElementById(this.containerValue).classList.add('border-2', 'border-orange-500');
    }

    onChangeTheme(event) {
        const { value: theme } = event.target;
        const blockElement = document.getElementById(this.containerValue);

        if (blockElement) {
          blockElement.dataset.theme = theme;
          this.themeValue = theme;
        } else {
            console.error(`Block with ID 'block-${this.containerValue}' not found.`);
        }
    }

    onChangeBackgroundColor(event) {
        const { value: backgroundColor } = event.target;

        const blockElement = document.getElementById(this.containerValue);
        const overlayBlockElement = document.getElementById(`${this.containerValue}-overlay`);

        if (blockElement) {
            blockElement.dataset.theme = backgroundColor;
        }

        if (overlayBlockElement) {
            overlayBlockElement.dataset.theme = backgroundColor;
        }

        //this.outerContainerLayerValue.back = backgroundColor;
    }

    onChangeAlignment(event) {
        const { value: alignment } = event.target;
        const blockElement = document.getElementById(this.containerValue);

        if (blockElement) {
            blockElement.classList.remove("alignment-" + this.alignmentValue);
            blockElement.classList.add("alignment-" +alignment);
            this.alignmentValue = alignment;
        } else {
           console.error(`Block ${this.containerValue} not found.`);
        }
    }

    onChangeMediaAlignment(event) {
        const {value: mediaAlignment} = event.target;
        const blockElement = document.getElementById(this.mediaContainerValue);

        console.log(this.mediaContainerValue)

        if (blockElement) {
            blockElement.classList.remove(this.mediaAlignmentValue);
            blockElement.classList.add(mediaAlignment);
            this.mediaAlignmentValue = mediaAlignment;
        } else {
            console.error(`Block ${this.containerValue} not found.`);
        }
    }
}