# frozen_string_literal: true

class Ui::ContentContainer < Phlex::HTML
  attr_reader :controls, :block_id, :content_layer, :css, :style

  def initialize(controls, block_id, content_layer, css: nil, style: nil)
    @controls = controls
    @block_id =  block_id
    @content_layer = content_layer
    @css = css
    @style = style
  end

  def view_template
    div(
      id: dom_id,
      class: "#{parse_css} #{@content_layer.padding_y} #{@content_layer.padding_x } #{@content_layer.container} alignment-#{@content_layer.alignment} #{@content_layer.gap_y} #{static_css}"
    ) do
      controls.each do |control|
        div(class: "control-container", data: { theme: "primary" }) do
          render control.component
        end
      end
    end
  end

  private

  def static_css
    "bg-auto outline-offset-2 mx-auto m-4 relative z-10 flex flex-col"
  end

  def dom_id
    "block-#{@block_id}-content-layer"
  end
end
