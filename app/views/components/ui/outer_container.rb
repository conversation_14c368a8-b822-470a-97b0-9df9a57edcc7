class Ui::OuterContainer < Phlex::HTML
  attr_reader :block_id, :outer_container_layer, :css

  def initialize(block_id, outer_container_layer, css: nil)
    @block_id = block_id
    @outer_container_layer = outer_container_layer
    @css = css
  end

  def view_template
    div(data: { theme: @outer_container_layer.theme }, class: "bg-base-100 #{parse_css}") do
      div(id: dom_id, data: { theme: @outer_container_layer.theme }, class: "#{@outer_container_layer.container}  outline-offset-2 mx-auto") do
        div(class: "#{container_classes}") do
          yield
        end
      end
    end
  end

  private

  def container_classes
    "overflow-hidden relative block #{@outer_container_layer.padding_y}"
  end

  def dom_id
    "block-#{@block_id}-outer-container-layer"
  end
end
