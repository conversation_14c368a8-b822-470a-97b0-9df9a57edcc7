module BlockViews
  class HeroViews::Hero005Component < BaseComponent
    def view_template
      container do
        inner_container do
            div(class: "overflow-hidden sm:grid sm:grid-cols-2 items-center") do
              media_container(css: @block_presenter.media_alignment_class) do
                if @block_presenter.context == :admin
                  if @block_presenter.media.items.any?
                    render Ui::Image(@block_presenter.media.items.first, resize_options: { resize_to_fill: [1000, 800] }, classes: "w-full h-full object-cover")
                  else
                    render Ui::Image("https://picsum.photos/800/600", classes: "w-full h-full object-cover")
                  end
                else
                  div(data: { controller: "splide" }, class: "relative splide #{admin? ? 'z-0' : ''}") do
                    div(class: "splide__track") do
                      ul(class: "splide__list") do
                        @block_presenter.media.items.each do |item|
                          li(class: "splide__slide") do
                            render Ui::Image(item, resize_options: { resize_to_fill: [1000, 800] }, classes: "w-full h-full object-cover")
                          end
                        end
                      end
                    end
                  end
                end
              end

              content_container(@block_presenter.controls)
            end
          end
        end
      end
    end
  end
end
