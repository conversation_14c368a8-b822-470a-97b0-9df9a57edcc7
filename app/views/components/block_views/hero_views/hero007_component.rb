module BlockViews
  class HeroViews::Hero007Component < BaseComponent
    def view_template
      container(css: "relative h-screen w-full ", style: "background-position: center; background-size: cover; background-image: url(https://images.unsplash.com/photo-1490645935967-10de6ba17061?q=80&w=3506&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)") do

        # Content centered
        div(class: "relative z-10 flex items-center justify-center h-full w-full") do
          inner_container(css: "rounded-box shadow-lg") do
            content_container(@block_object.controls)
          end
        end
      end
    end
  end
end
