module BlockViews
  class HeroViews::Hero008Component < BaseComponent
    def view_template
      container do
      inner_container(css: "relative isolate px-6 lg:px-8") do
          if @block_object.background_image
            div(class: "absolute inset-0 -z-10 w-full h-full opacity-20") do
              img(src: url_for(@block_object.background_image), class: "w-full h-full object-cover")
            end
          else
            div(class: "absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80", aria_hidden: "true") do
              div(
                class: "relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary to-accent opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]",
                style: "clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
              )
            end
          end

          content_container(css: "mx-auto text-center") do
            @block_object.controls.each do |control|
              render control.component
            end
          end

          if @block_object.background_image_mobile
            div(class: "absolute inset-x-0 bottom-0 -z-10 w-full h-1/2 opacity-10") do
              img(src: url_for(@block_object.background_image_mobile), class: "w-full h-full object-cover")
            end
          else
            div(class: "absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]", aria_hidden: "true") do
              div(
                class: "relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-accent to-primary opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]",
                style: "clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
              )
            end
          end
        end
      end
    end
  end
end
