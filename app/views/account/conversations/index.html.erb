<div class="container mx-auto">
  <div class="flex my-8 space-x-6">
    <%= render partial: 'menu' %>

    <div class="w-full ">
      <% if @conversations.empty? %>
        <div class="rounded-md bg-blue-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3 flex-1 md:flex md:justify-between">
              <p class="text-sm text-blue-700">Zatím nemáte žádné konverzace.</p>
            </div>
          </div>
        </div>
      <% end %>

      <ul role="list" class="divide-y divide-gray-100 max-w-xl mx-auto">
        <% @conversations.each do |conversation| %>
          <div class="flex flex-wrap items-center justify-between gap-x-6 gap-y-4 py-5 sm:flex-nowrap <%= conversation.has_unread_messages_by_user(current_user) ? 'bg-orange-50' : 'bg-white' %>">
            <div class="pl-3">
              <p class="text-sm font-semibold leading-6 text-gray-900">
                <a href="<%= account_conversation_path(conversation) %>" class="hover:underline">
                  <%= conversation.name %>
                </a>
              </p>
              <div class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                <p>
                  <a href="<%= account_conversation_path(conversation) %>" class="hover:underline">
                    <%= conversation.messages.last.user.email %>
                  </a>
                </p>
                <svg viewBox="0 0 2 2" class="h-0.5 w-0.5 fill-current">
                  <circle cx="1" cy="1" r="1" />
                </svg>
                <p><%= l conversation.updated_at %></p>
              </div>
            </div>
            <dl class="flex w-full flex-none justify-between gap-x-8 sm:w-auto">
              <div class="flex w-16 gap-x-2.5">
                <dt>
                  <span class="sr-only">Total comments</span>
                  <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 011.037-.443 48.282 48.282 0 005.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
                  </svg>
                </dt>
                <dd class="text-sm leading-6 text-gray-900">
                  <%= conversation.messages.count %>
                </dd>
              </div>
            </dl>
          </div>
        <% end %>
      </ul>
    </div>
  </div>
</div>