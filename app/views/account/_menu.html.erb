<div class="sm:w-1/4">
  <div class="bg-white rounded overflow-hidden shadow-lg">
    <div class="text-center p-6 border-b">
      <div class="flex justify-center">
        <%= account_avatar current_user, class: "rounded-full h-12 w-12" %>
      </div>

      <p class="pt-2 text-lg font-semibold"><%= current_user.first_name %> <%= current_user.surname %></p>
      <p class="text-sm text-gray-600"><%= current_user.email %></p>
      <div class="mt-5">
        <a href="<%= settings_account_path %>" class="border rounded-full py-2 px-4 text-xs font-semibold text-gray-700 hover:bg-gray-50">
          Nastavení
        </a>
      </div>
    </div>
    <div class="border-b">
      <a href="<%= account_path %>" class="px-4 py-2 hover:bg-gray-100 flex">
        <div class="text-gray-800">
          <i class="fas fa-clipboard text-gray-400"></i>
        </div>
        <div class="pl-3">
          <p class="text-sm font-medium text-gray-800 leading-none">Přehled</p>
          <p class="text-xs text-gray-500">předplatné a objednávky</p>
        </div>
      </a>

      <a href="<%= account_library_index_path %>" class="px-4 py-2 hover:bg-gray-100 flex justify-between">
        <div class="flex">
          <div class="text-gray-800">
            <i class="fas fa-tablet text-gray-400"></i>
          </div>
          <div class="pl-3">
            <p class="text-sm font-medium text-gray-800 leading-none">E-knihovna</p>
            <p class="text-xs text-gray-500">časopis online</p>
          </div>
        </div>
        <div>
          <% if current_user.payment_processor&.subscribed? %>
            <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">Aktivní</span>
        <% else %>
            <span class="inline-flex items-center rounded-full bg-red-100 px-3 py-0.5 text-sm font-medium text-red-800">Neaktivní</span>
          <% end %>
        </div>
      </a>

      <a href="#" class="px-4 py-2 hover:bg-gray-100 flex">
        <div class="text-green-600">
          <i class="fas fa-id-card-alt text-gray-400"></i>
        </div>
        <div class="pl-3">
          <p class="text-sm font-medium text-gray-800 leading-none">
            Profil
          </p>
          <p class="text-xs text-gray-500">
            veřejný profil
          </p>
        </div>
      </a>

      <% if current_user.author? %>
        <a href="<%= account_author_path %>" class="px-4 py-2 hover:bg-gray-100 flex">
          <div class="text-green-600">
            <i class="fas fa-id-card-alt text-gray-400"></i>
          </div>
          <div class="pl-3">
            <p class="text-sm font-medium text-gray-800 leading-none">
              Autor
            </p>
            <p class="text-xs text-gray-500">
              autorský profil
            </p>
          </div>
        </a>
        <% end %>
    </div>

    <div class="">
      <%= button_to destroy_user_session_path, method: :delete, class: 'w-full text-left cursor-pointer px-6 py-3 text-gray-800 bg-white hover:bg-primary-100 transition ease-in-out duration-200 whitespace-nowrap' do %>
        <p class="text-xs font-medium text-gray-600 leading-none">Odhlásit se</p>
      <% end %>
    </div>
  </div>
</div>