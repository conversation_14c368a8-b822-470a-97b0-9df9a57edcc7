<% shipping_array = Shipping.all.map { |shipping| [shipping.name, shipping.id] } %>

<%= form_with model: @shipment, url: admin_shipments_path, local: true do |f| %>
  <div class="form-group">
    <label>Odeslat přes:</label>
    <%= f.select(:shipping_id, options_for_select(shipping_array), {}, { :class => 'block appearance-none w-full border border-gray-200 text-gray-700 py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-white focus:border-gray-500' }) %>
  </div>

  <% @shipment.packages.each do |package| %>
    <ul class="w-full px-4 py-3 bg-white shadow-md rounded-md my-8">
      <div class="flex divide-x divide-gray-500 space-x-8 items-center">
        <div class="whitespace-nowrap w-2/6">
                <span class="text-md text-gray-800">
                  Objednávka č. <%= package.order.id %> <br />
                  <strong>váha cca <%= package.order.weight / 1000 %> kg</strong> <br />
                </span>

          <%== package.order.invoice.billing_address %>

          <div class="bg-primary-400 text-primary-800 px-2 py-1 uppercase text-xs mt-2">
            <%= f.fields_for :packages, package do |order| %>
              <%= order.hidden_field :order_id %>
              <%= order.select(:shipping_service_name, ::Shipment::PACKAGE_TYPES, { selected: package.order.weight >= 2000 ? 'dr' : 'rr' }, { class: 'block appearance-none w-full border border-gray-200 text-gray-700 py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-white focus:border-gray-500' }) %>
            <% end %>
          </div>
        </div>
        <ul role="list" class="mt-5 grid grid-cols-8 gap-2 pl-6">
          <% package.order.invoice.products.compact.each do |invoice_item| %>
            <li class="relative">
              <div class="block">
                <%= image_tag invoice_item.product.cover.variant(resize_to_limit: [70, nil]),  class: "block" if invoice_item.product.cover.attached? %>
              </div>
              <p class="text-sm py-2">
                <strong><%= invoice_item.quantity %>x</strong> <%= invoice_item.name %>
                <br />- <%= price_format invoice_item.price_unit %> <%= package.order.invoice.currency %>
              </p>
            </li>
          <% end %>
        </ul>
      </div>
    </ul>
  <% end %>

    <%= submit_tag('Vytvořit zásilku', :class => 'bg-eshop-500 text-white p-2') %>
<% end %>