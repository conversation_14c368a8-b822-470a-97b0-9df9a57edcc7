<div class="flex justify-between">
  <h1 class="text-3xl font-semibold"><PERSON><PERSON><PERSON> str<PERSON><PERSON></h1>
  <div>
    <a href="<%= new_admin_page_path %>" class="bg-rose-500 hover:bg-rose-600 text-white p-2">P<PERSON><PERSON><PERSON> strán<PERSON></a>
  </div>
</div>

<div class="mt-8">
  <div class="mt-8 flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
            <tr class="divide-x divide-gray-200">
              <th scope="col" class="px-4 py-3.5 text-left text-sm font-semibold text-gray-900"><PERSON><PERSON><PERSON><PERSON></th>
              <th scope="col" class="px-4 py-3.5 text-left text-sm font-semibold text-gray-900">URL</th>
              <th scope="col" class="py-3.5 pl-4 pr-4 text-left text-sm font-semibold text-gray-900 sm:pr-6">Akce</th>
            </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
            <% @pages.each do |page| %>
              <tr class="divide-x divide-gray-200">
                <td class="whitespace-nowrap p-2 text-sm">
                  <div class="flex items-center space-x-2">
                    <span><%= page.title %></span>
                  </div>
                </td>
                <td class="whitespace-nowrap p-2 text-sm">
                  <div class="flex items-center space-x-2">
                    <span>/<%= page.slug %></span>
                  </div>
                </td>
                <td>
                  <div class="flex align-middle flex items-center justify-center space-x-2">
                    <%= link_to edit_admin_page_path(page), class: "inline-flex items-center rounded-full border border-transparent bg-green-600 p-1 text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                      </svg>
                    <% end %>

                    <%= button_to admin_page_path(page), method: :delete, class: "inline-flex items-center rounded-full border border-transparent bg-red-600 p-1 text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5">
                        <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                      </svg>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>