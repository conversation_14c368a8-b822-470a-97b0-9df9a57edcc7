<div class="mb-6 flex justify-between items-center">
  <div>
    <div class="flex items-center space-x-2">
      <a href="<%= admin_merchant_merchant_pages_path(@merchant_page.merchant) %>" class="btn btn-sm btn-outline">← Zpět na seznam stránek</a>
      <h1 class="text-2xl font-bold">Detail stránky obchodníka</h1>
    </div>
    <p class="text-gray-500">ID: <%= @merchant_page.id %></p>
  </div>
  <div class="flex space-x-2">
    <a href="<%= edit_admin_merchant_merchant_page_path(@merchant_page.merchant, @merchant_page) %>" class="btn btn-outline">Upravit</a>
    <a href="<%= sync_admin_merchant_merchant_page_path(@merchant_page.merchant, @merchant_page) %>" class="btn btn-info" data-turbo-method="post">Synchronizovat</a>
  </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title">Základní informace</h2>

      <div class="grid grid-cols-2 gap-4 mt-4">
        <div>
          <p class="text-sm text-gray-500">Název</p>
          <p class="font-medium"><%= @merchant_page.name.presence || 'Bez názvu' %></p>
        </div>

        <div>
          <p class="text-sm text-gray-500">Obchodník</p>
          <p class="font-medium">
            <a href="<%= admin_merchant_path(@merchant_page.merchant) %>" class="text-primary hover:underline">
              <%= @merchant_page.merchant.name %>
            </a>
          </p>
        </div>

        <div class="col-span-2">
          <p class="text-sm text-gray-500">URL</p>
          <p class="font-medium">
            <a href="<%= @merchant_page.url %>" target="_blank" class="text-primary hover:underline">
              <%= @merchant_page.url %>
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title">Stav synchronizace</h2>

      <div class="grid grid-cols-2 gap-4 mt-4">
        <div>
          <p class="text-sm text-gray-500">Stav</p>
          <p class="font-medium">
            <% case @merchant_page.status %>
            <% when 'pending' %>
              <span class="badge badge-warning">Čeká na synchronizaci</span>
            <% when 'syncing' %>
              <span class="badge badge-info">Probíhá synchronizace</span>
            <% when 'success' %>
              <span class="badge badge-success">Synchronizováno</span>
            <% when 'error' %>
              <span class="badge badge-error">Chyba</span>
            <% end %>
          </p>
        </div>

        <div>
          <p class="text-sm text-gray-500">Poslední synchronizace</p>
          <p class="font-medium"><%= @merchant_page.last_synced_at ? l(@merchant_page.last_synced_at, format: :long) : 'Nikdy' %></p>
        </div>

        <% if @merchant_page.error_message.present? %>
          <div class="col-span-2">
            <p class="text-sm text-gray-500">Chybová zpráva</p>
            <div class="bg-red-50 p-3 rounded-lg text-red-700 text-sm mt-1">
              <%= simple_format @merchant_page.error_message %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<div class="card bg-base-100 shadow-xl">
  <div class="card-body">
    <h2 class="card-title">Historie synchronizací</h2>
    
    <div class="overflow-x-auto mt-4">
      <% if @reports.any? %>
        <table class="table table-zebra w-full">
          <thead>
            <tr>
              <th>ID</th>
              <th>Datum a čas</th>
              <th>Stav</th>
              <th>Celkem produktů</th>
              <th>Aktualizováno</th>
              <th>Vytvořeno</th>
              <th>Chyby</th>
              <th>Doba trvání</th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <% @reports.each do |report| %>
              <tr>
                <td><%= report.id %></td>
                <td><%= l(report.started_at, format: :short) if report.started_at %></td>
                <td>
                  <% case report.status %>
                  <% when 'processing' %>
                    <span class="badge badge-info">Zpracovává se</span>
                  <% when 'success' %>
                    <span class="badge badge-success">Úspěch</span>
                  <% when 'error' %>
                    <span class="badge badge-error">Chyba</span>
                  <% end %>
                </td>
                <td><%= report.total_products %></td>
                <td><%= report.updated_products %></td>
                <td><%= report.created_products %></td>
                <td><%= report.error_count %></td>
                <td><%= report.formatted_duration %></td>
                <td>
                  <a href="<%= admin_merchant_page_process_report_path(report) %>" class="btn btn-sm">Detail</a>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="alert alert-info">
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span>Žádné záznamy o synchronizaci.</span>
          </div>
        </div>
      <% end %>
    </div>
    
    <div class="mt-4">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  </div>
</div>
