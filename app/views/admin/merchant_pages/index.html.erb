<div class="mb-6 flex justify-between items-center">
  <div>
    <div class="flex items-center space-x-2">
      <a href="<%= admin_merchant_path(@merchant) %>" class="btn btn-sm btn-outline">← Zpět na obchodníka</a>
      <h1 class="text-2xl font-bold">URL adresy obchodníka <%= @merchant.name %></h1>
    </div>
  </div>
  <div class="flex space-x-2">
    <%= button_to "Synchronizovat všechny URL", sync_admin_merchant_path(@merchant), method: :post, class: "btn btn-success", data: { turbo_confirm: "Opravdu chcete spustit synchronizaci všech URL adres tohoto obchodníka?" } %>
    <a href="<%= new_admin_merchant_merchant_page_path(@merchant) %>" class="btn btn-primary">Přidat URL</a>
  </div>
</div>

<div class="card bg-base-100 shadow-xl">
  <div class="card-body">
    <h2 class="card-title">Se<PERSON><PERSON> stránek</h2>

    <div class="overflow-x-auto mt-4">
      <% if @merchant_pages.any? %>
        <table class="table table-zebra w-full">
          <thead>
            <tr>
              <th>URL</th>
              <th>Stav</th>
              <th>Poslední synchronizace</th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <% @merchant_pages.each do |page| %>
              <tr>
                <td>
                  <a href="<%= page.url %>" target="_blank" class="text-primary hover:underline">
                    <%= truncate(page.url, length: 80) %>
                  </a>
                </td>
                <td>
                  <% case page.status %>
                  <% when 'pending' %>
                    <span class="badge badge-warning">Čeká na synchronizaci</span>
                  <% when 'syncing' %>
                    <span class="badge badge-info">Probíhá synchronizace</span>
                  <% when 'success' %>
                    <span class="badge badge-success">Synchronizováno</span>
                  <% when 'error' %>
                    <span class="badge badge-error">Chyba</span>
                  <% end %>
                </td>
                <td><%= page.last_synced_at ? l(page.last_synced_at, format: :short) : 'Nikdy' %></td>
                <td>
                  <%= button_to "Smazat", admin_merchant_merchant_page_path(@merchant, page), method: :delete, class: "btn btn-sm btn-error", data: { turbo_confirm: "Opravdu chcete smazat tuto URL adresu?" } %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="alert alert-info">
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span>Obchodník nemá žádné stránky pro synchronizaci cen.</span>
          </div>
        </div>
      <% end %>
    </div>

    <div class="mt-4">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  </div>
</div>
