<%= form_with(model: [:admin, @magazine], local: true) do |f| %>
  <% if @magazine.errors.any? %>
    <div id="error_explanation">
      <ul>
        <% @magazine.errors.full_messages.each do |msg| %>
          <li><%= msg %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group w-full">
    <%= f.label :year %>
    <%= f.text_field :year, :class => 'form-input w-full' %>
  </div>

  <div class="form-group w-full mt-4">
    <%= f.label :number %>
    <%= f.text_field :number, :class => 'form-input w-full' %>
  </div>

  <div class="form-group w-full mt-4">
    <%= f.label :released_at %>
    <%= f.date_field :released_at, :class => 'form-input w-full' %>
  </div>

  <div class="form-group w-full mt-4">
    <%= f.label :flipbook_url %>
    <%= f.text_field :flipbook_url, :class => 'form-input w-full' %>
  </div>

  <div class="form-group w-full mt-4">
    <%= f.label :flipbook_preview_url %>
    <%= f.text_field :flipbook_preview_url, :class => 'form-input w-full' %>
  </div>

  <div class="form-group w-full mt-4">
    <%= f.label :download_url %>
    <%= f.text_field :download_url, :class => 'form-input w-full' %>
  </div>

  <div class="form-group mt-4">
    <div class="flex items-center space-x-4">
      <% if @magazine.cover.presence %>
        <div>
          <%= image_tag @magazine.cover.variant(resize_to_limit: [100, nil]),  class: "img-fluid d-block" %>
        </div>
      <% end %>

      <div>
        <%= f.label :cover %>
        <%= f.file_field :cover, accept:'image/*', class: "input-file" %>
      </div>
    </div>
  </div>

  <hr class="my-4">

  <h4 class="font-bold text-lg mb-2">Obsah čísla</h4>

  <div class="form-group">
    <%= f.fields_for :contents do |content| %>
      <div class="flex space-x-3 mb-4">
        <div>
          <%= content.label :article %>
          <%= content.text_field :article, class: "form-input" %>
        </div>

        <div>
          <%= content.label :author %>
          <%= content.text_field :author, class: "form-input" %>
        </div>

        <div>
          <%= content.label :page %>
          <%= content.text_field :page, class: "form-input" %>
        </div>

        <div>
          <%= content.label :species %>
          <%= content.text_field :species, class: "form-input" %>
        </div>

        <div>
          <%= content.label :topic %>
          <%= content.text_field :topic, class: "form-input" %>
        </div>
      </div>
    <% end %>
  </div>

  <%= f.submit nil, :class => 'inline-flex mt-3 items-center px-5 py-2 border border-transparent text-base font-medium rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500' %>
<% end %>