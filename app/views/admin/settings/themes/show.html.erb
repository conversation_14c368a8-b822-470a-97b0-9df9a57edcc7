<%= form_with model: [:admin, current_tenant], url: admin_settings_theme_path, method: :patch do |f| %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <h1 class="text-2xl font-semibold text-gray-900">
              Téma webu
            </h1>
          </div>

          <!-- Přednastavená témata -->
          <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Přednastavená témata</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <% current_tenant.available_theme_presets.each do |key, preset| %>
                <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                  <h3 class="font-medium text-gray-900 mb-2"><%= preset[:name] %></h3>
                  <div class="flex space-x-2 mb-3">
                    <% if preset.dig(:colors, :primary, :base) %>
                      <div class="w-6 h-6 rounded border border-gray-200" style="background-color: <%= preset.dig(:colors, :primary, :base) %>"></div>
                    <% end %>
                    <% if preset.dig(:colors, :soft, :base) %>
                      <div class="w-6 h-6 rounded border border-gray-200" style="background-color: <%= preset.dig(:colors, :soft, :base) %>"></div>
                    <% end %>
                    <% if preset.dig(:colors, :primary, :accent) %>
                      <div class="w-6 h-6 rounded border border-gray-200" style="background-color: <%= preset.dig(:colors, :primary, :accent) %>"></div>
                    <% end %>
                  </div>
                  <%= link_to "Načíst téma", 
                      load_preset_admin_settings_theme_path(preset_key: key), 
                      method: :patch,
                      class: "btn btn-sm btn-outline",
                      data: { confirm: "Opravdu chcete načíst toto téma? Aktuální nastavení bude přepsáno." } %>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Vlastní nastavení -->
          <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Vlastní nastavení</h2>
            <div class="bg-white flex flex-col space-y-6">
              
              <!-- Barvy -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Primární barva</label>
                  <%= f.color_field :primary_color, 
                      value: current_tenant.primary_color || current_tenant.current_theme_preset.dig(:colors, :primary, :base),
                      class: "w-full h-10 border border-gray-300 rounded-md" %>
                  <p class="text-xs text-gray-500 mt-1">Hlavní barva webu (tlačítka, odkazy)</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Sekundární barva</label>
                  <%= f.color_field :secondary_color, 
                      value: current_tenant.secondary_color || current_tenant.current_theme_preset.dig(:colors, :soft, :base),
                      class: "w-full h-10 border border-gray-300 rounded-md" %>
                  <p class="text-xs text-gray-500 mt-1">Barva pozadí a doplňkových prvků</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Akcentní barva</label>
                  <%= f.color_field :accent_color, 
                      value: current_tenant.accent_color || current_tenant.current_theme_preset.dig(:colors, :primary, :accent),
                      class: "w-full h-10 border border-gray-300 rounded-md" %>
                  <p class="text-xs text-gray-500 mt-1">Barva pro zvýraznění důležitých prvků</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Barva textu</label>
                  <%= f.color_field :base_content_color, 
                      value: current_tenant.base_content_color || current_tenant.current_theme_preset.dig(:colors, :soft, :base_content),
                      class: "w-full h-10 border border-gray-300 rounded-md" %>
                  <p class="text-xs text-gray-500 mt-1">Základní barva textu</p>
                </div>
              </div>

              <!-- Typografie -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Rodina písma</label>
                <%= f.select :font_family, 
                    options_for_select([
                      ['Inter (výchozí)', 'Inter, system-ui, sans-serif'],
                      ['Roboto', 'Roboto, sans-serif'],
                      ['Open Sans', 'Open Sans, sans-serif'],
                      ['Lato', 'Lato, sans-serif'],
                      ['Poppins', 'Poppins, sans-serif'],
                      ['Montserrat', 'Montserrat, sans-serif'],
                      ['Source Sans Pro', 'Source Sans Pro, sans-serif']
                    ], current_tenant.font_family || 'Inter, system-ui, sans-serif'),
                    { prompt: "Vyberte písmo" },
                    { class: "select w-full max-w-xs" } %>
              </div>

              <!-- Styl prvků -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Zaoblení rohů</label>
                  <%= f.select :border_radius, 
                      options_for_select([
                        ['Žádné (0px)', '0'],
                        ['Malé (4px)', '0.25rem'],
                        ['Střední (8px)', '0.5rem'],
                        ['Velké (12px)', '0.75rem'],
                        ['Extra velké (16px)', '1rem']
                      ], current_tenant.border_radius || '0.5rem'),
                      { prompt: "Vyberte zaoblení" },
                      { class: "select w-full max-w-xs" } %>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Styl tlačítek</label>
                  <%= f.select :button_style, 
                      options_for_select([
                        ['Plné', 'solid'],
                        ['Obrysové', 'outline'],
                        ['Průhledné', 'ghost']
                      ], current_tenant.button_style || 'solid'),
                      { prompt: "Vyberte styl" },
                      { class: "select w-full max-w-xs" } %>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-avocado-100/70 border-t-2 border-avocado-200">
    <div class="max-w-4xl mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>
