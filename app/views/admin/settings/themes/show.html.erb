<!-- <PERSON><PERSON><PERSON><PERSON><PERSON> (mimo <PERSON>) -->
<div class="relative bg-white">
  <div class="flex-1 focus:outline-none">
    <div class="relative max-w-6xl mx-auto md:px-8 xl:px-0">
      <div class="py-6">
        <div class="px-4 sm:px-6 md:px-0">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Šablony</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <% current_tenant.available_templates.each do |key, template| %>
              <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors <%= 'border-avocado-500 bg-avocado-50' if current_tenant.template_key == key.to_s %>">
                <h3 class="font-medium text-gray-900 mb-2"><%= template[:name] %></h3>
                <div class="flex space-x-2 mb-3">
                  <% template.dig(:colors)&.each do |theme_name, theme_data| %>
                    <% next unless theme_data.is_a?(Hash) && theme_data[:base] %>
                    <div class="w-4 h-4 rounded border border-gray-200"
                         style="background-color: <%= theme_data[:base] %>"
                         title="<%= theme_data[:name] || theme_name.to_s.humanize %>"></div>
                  <% end %>
                </div>
                <%= button_to "Načíst šablonu",
                    load_preset_admin_settings_theme_path,
                    method: :patch,
                    params: { template_key: key },
                    class: "btn btn-sm #{'btn-primary' if current_tenant.template_key == key.to_s} #{'btn-outline' unless current_tenant.template_key == key.to_s}",
                    data: { confirm: "Opravdu chcete načíst tuto šablonu? Aktuální nastavení bude přepsáno." } %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%= form_with model: [:admin, current_tenant], url: admin_settings_theme_path, method: :patch do |f| %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-6xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <h1 class="text-2xl font-semibold text-gray-900">
              Úprava tématu
            </h1>
          </div>



          <!-- Úprava barevných témat -->
          <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Barevná témata</h2>
            <div class="space-y-8">
              <% current_tenant.available_color_themes.each do |theme_name, theme_data| %>
                <% next unless theme_data.is_a?(Hash) && theme_data.key?(:base) %>
                <div class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-md font-medium text-gray-900 mb-4">
                    <%= theme_data[:name] || theme_name.to_s.humanize %>
                    <span class="text-sm text-gray-500 font-normal">(<%= theme_name %>)</span>
                  </h3>

                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Základní barva</label>
                      <%= f.color_field "#{theme_name}_base",
                          value: current_tenant.send("#{theme_name}_base") || theme_data[:base],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva pozadí</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Barva textu</label>
                      <%= f.color_field "#{theme_name}_base_content",
                          value: current_tenant.send("#{theme_name}_base_content") || theme_data[:base_content],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva textu na pozadí</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Akcentní barva</label>
                      <%= f.color_field "#{theme_name}_accent",
                          value: current_tenant.send("#{theme_name}_accent") || theme_data[:accent],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva pro zvýraznění</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Barva textu na akcentu</label>
                      <%= f.color_field "#{theme_name}_accent_content",
                          value: current_tenant.send("#{theme_name}_accent_content") || theme_data[:accent_content],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva textu na akcentní barvě</p>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Obecná nastavení -->
          <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Obecná nastavení</h2>
            <div class="bg-white flex flex-col space-y-6">

              <!-- Typografie -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Rodina písma</label>
                <%= f.select :font_family,
                    options_for_select([
                      ['Inter (výchozí)', 'Inter, system-ui, sans-serif'],
                      ['Roboto', 'Roboto, sans-serif'],
                      ['Open Sans', 'Open Sans, sans-serif'],
                      ['Lato', 'Lato, sans-serif'],
                      ['Poppins', 'Poppins, sans-serif'],
                      ['Montserrat', 'Montserrat, sans-serif'],
                      ['Source Sans Pro', 'Source Sans Pro, sans-serif']
                    ], current_tenant.font_family || 'Inter, system-ui, sans-serif'),
                    { prompt: "Vyberte písmo" },
                    { class: "select w-full max-w-xs" } %>
              </div>

              <!-- Styl prvků -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Zaoblení rohů</label>
                  <%= f.select :border_radius,
                      options_for_select([
                        ['Žádné (0px)', '0'],
                        ['Malé (4px)', '0.25rem'],
                        ['Střední (8px)', '0.5rem'],
                        ['Velké (12px)', '0.75rem'],
                        ['Extra velké (16px)', '1rem']
                      ], current_tenant.border_radius || current_tenant.current_template.dig(:radius, :field) || '0.5rem'),
                      { prompt: "Vyberte zaoblení" },
                      { class: "select w-full max-w-xs" } %>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Styl tlačítek</label>
                  <%= f.select :button_style,
                      options_for_select([
                        ['Plné', 'solid'],
                        ['Obrysové', 'outline'],
                        ['Průhledné', 'ghost']
                      ], current_tenant.button_style || 'solid'),
                      { prompt: "Vyberte styl" },
                      { class: "select w-full max-w-xs" } %>
                </div>
              </div>

              <!-- Skryté pole pro template_key -->
              <%= f.hidden_field :template_key, value: current_tenant.template_key || 'default' %>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-avocado-100/70 border-t-2 border-avocado-200">
    <div class="max-w-6xl mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>
