<% if filter_history.present? %>
  <div class="mt-3">
    <div class="flex items-center space-x-2">
      <div class="hidden sm:block">
        <svg xmlns="http://www.w3.org/2000/svg" class="size-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <div class="flex flex-col items-center w-full">
        <div class="grid grid-cols-3 gap-3 w-full">
          <% filter_history.each_with_index do |filter, index| %>
            <% if filter.has_filters? %>
              <% category = Category.find_by(id: filter.category) %>
              <% next if category.nil? %>

              <%= link_to products_path(category.slug, filter.to_params.except(:category)), class: "bg-white/80 backdrop-blur-sm rounded-box p-3 shadow-sm border border-primary/20 hover:border-primary hover:shadow transition-all flex items-center h-full" do %>
                <div class="text-xs">
                <span class="font-medium">
                  <% if filter.category.present? %>
                    <%= category.name %>
                  <% end %>
                  <% if filter.season.present? %>
                    <%= t("products.seasons.#{filter.season}") %>
                  <% end %>
                  <% if filter.width.present? %>
                    <%= filter.width %>
                  <% end %>
                  <% if filter.profile.present? %>
                    / <%= filter.profile %>
                  <% end %>
                  <% if filter.diameter.present? %>
                    R<%= filter.diameter %>
                  <% end %>
                  <% if filter.manufacturer.present? && Manufacturer.find_by(id: filter.manufacturer).present? %>
                    <%= Manufacturer.find_by(id: filter.manufacturer)&.name %>
                  <% end %>
                  <% if filter.product.present? && Product.find_by(id: filter.product).present? %>
                    <%= Product.find_by(id: filter.product)&.name %>
                  <% end %>
                </span>
                </div>
              <% end %>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
<% end %>
