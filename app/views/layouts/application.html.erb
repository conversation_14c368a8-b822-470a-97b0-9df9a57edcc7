<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
  <head>
    <%= display_meta_tags site: nil, reverse: true %>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", layout: "module" %>

    <link href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css" rel="stylesheet">

    <style id="styles">
        :root {
            <%= generate_color_variables_from_yaml(@colors) %>
            --custom-radius-selector: <%= @theme[:radius][:selector] %>;
            --custom-radius-field: <%= @theme[:radius][:field] %>;
            --custom-radius-box: <%= @theme[:radius][:box] %>;
            --custom-size-selector: <%= @theme[:sizes][:selector] %>;
            --custom-size-field: <%= @theme[:sizes][:field] %>;
            --custom-options-border: <%= @theme[:options][:border] %>;
            --custom-options-depth: <%= @theme[:options][:depth] %>;
            --custom-options-noise: <%= @theme[:options][:noise] %>;
            --decorative-font: "Dancing Script", cursive;

            /* Uživatelské nastavení tématu z databáze */
            <%= generate_theme_css_variables %>
        }

        /* Aplikace uživatelských nastavení */
        <% if current_tenant&.theme&.any? %>
        body {
            font-family: var(--theme-font-family, <%= @theme_settings&.dig(:font_family) || 'Inter, system-ui, sans-serif' %>);
        }

        .btn, .button {
            border-radius: var(--theme-border-radius, <%= @theme_settings&.dig(:border_radius) || '0.5rem' %>);
            background-color: var(--theme-primary-color, <%= @theme_settings&.dig(:primary_color) %>);
            color: var(--theme-accent-content-color, <%= @theme_settings&.dig(:accent_content_color) %>);
        }

        .btn:hover, .button:hover {
            background-color: var(--theme-accent-color, <%= @theme_settings&.dig(:accent_color) %>);
        }

        .bg-primary {
            background-color: var(--theme-primary-color, <%= @theme_settings&.dig(:primary_color) %>) !important;
        }

        .text-primary {
            color: var(--theme-primary-color, <%= @theme_settings&.dig(:primary_color) %>) !important;
        }
        <% end %>
    </style>

    <%# @template.template_html_tags.where(position: 0).each do |html_tag| %>
      <%#= html_tag.content.html_safe %>
    <%# end %>

  </head>

  <body class="flex flex-col min-h-screen">
    <%= render_header %>

    <div class="flex-1">
      <%= yield %>
    </div>

    <%= render "shared/footer_with_map", page_nodes: pages %>

    <%# @template.template_html_tags.where(position: 1).each do |html_tag| %>
    <%#= html_tag.content.html_safe %>
  <%# end %>
  </body>
</html>