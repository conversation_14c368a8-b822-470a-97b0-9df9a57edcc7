<div class="bg-white">
  <div class="mx-auto px-4 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
    <!-- Breadcrumbs navigace -->
    <nav class="flex mb-6 text-sm text-gray-500" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2">
        <li>
          <a href="<%= root_path %>" class="hover:text-gray-700">Domů</a>
        </li>
        <li class="flex items-center">
          <svg class="h-5 w-5 flex-shrink-0 text-gray-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <a href="<%= products_path %>" class="ml-2 hover:text-gray-700">Pneumatiky</a>
        </li>
        <li class="flex items-center">
          <svg class="h-5 w-5 flex-shrink-0 text-gray-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <a href="<%= "/pneumatiky/#{@manufacturer.slug}" %>" class="ml-2 hover:text-gray-700"><%= @manufacturer.name %></a>
        </li>
        <li class="flex items-center">
          <svg class="h-5 w-5 flex-shrink-0 text-gray-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <a href="<%= "/pneumatiky/#{@manufacturer.slug}/#{@product.slug}" %>" class="ml-2 hover:text-gray-700"><%= @product.name %></a>
        </li>
        <li class="flex items-center">
          <svg class="h-5 w-5 flex-shrink-0 text-gray-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <span class="ml-2 font-medium text-gray-900"><%= @variant.name %></span>
        </li>
      </ol>
    </nav>

    <div class="lg:grid lg:grid-cols-12 lg:gap-x-10">
      <!-- Levý sloupec s obrázkem -->
      <div class="lg:col-span-5">
        <div class="overflow-hidden bg-white border border-gray-200 rounded-lg relative py-2">
          <%= image_tag @product.image.variant(resize_to_limit: [500, 340]), class: "mx-auto" %>
          <!-- Badge pro sezónu přímo v obrázku -->
          <div class="absolute top-4 right-4 flex items-center space-x-2">
            <div class="shadow-lg rounded-full scale-125">
              <%= badge(@product.season) %>
            </div>
          </div>
        </div>

        <div class="mt-4 bg-gray-50">
          <div class="p-2">
            <h3 class="text-base font-medium leading-6 text-gray-900">Další dostupné rozměry</h3>
          </div>
          <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
              <% @product.variants.where.not(id: @variant.id).order(diameter: :asc, width: :asc).limit(3).each do |variant| %>
                <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt class="text-sm font-medium text-gray-900"><%= variant.name %></dt>
                  <dd class="mt-1 text-sm text-gray-900 sm:col-span-1 sm:mt-0">
                    <span class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                      Skladem
                    </span>
                  </dd>
                  <dd class="mt-1 text-sm text-right sm:col-span-1 sm:mt-0">
                    <a href="<%= product_variant_path(manufacturer_slug: @manufacturer.slug, product_slug: @product.slug, variant_slug: variant.slug) %>" class="font-medium text-primary-600 hover:text-primary-500">
                      od <%= price variant.cheapest.price %>
                    </a>
                  </dd>
                </div>
              <% end %>

              <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-b from-transparent to-white opacity-70 pointer-events-none z-10"></div>
                <dt class="text-sm font-medium text-gray-900 blur-[1px]">215/55 R17</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:col-span-1 sm:mt-0 blur-[1px]">
                  <span class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                    Skladem
                  </span>
                </dd>
                <dd class="mt-1 text-sm text-right sm:col-span-1 sm:mt-0 blur-[1px]">
                  <span class="font-medium text-primary-600">
                    od 1 599 Kč
                  </span>
                </dd>
              </div>

              <div class="py-3 sm:px-6 text-center border-t border-gray-200">
                <a href="<%= "/pneumatiky/#{@manufacturer.slug}/#{@product.slug}" %>" class="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-500">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
                    <path fill-rule="evenodd" d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z" clip-rule="evenodd" />
                  </svg>

                  Zobrazit všechny rozměry
                </a>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <!-- Pravý sloupec s informacemi o produktu, nabídkami obchodů a dalšími rozměry -->
      <div class="mt-8 lg:col-span-7 lg:mt-0">
        <!-- Informace o produktu -->
        <div class="mb-6">
          <div class="flex items-center">
            <h2 class="text-lg font-medium text-gray-900"><%= @manufacturer.name %></h2>
          </div>
          <h1 class="mt-1 text-2xl font-bold tracking-tight text-gray-900">
            <%= @product.name %> <span class="text-primary-600"><%= @variant.name %></span>
          </h1>
        </div>

        <div class="bg-white rounded-lg border border-gray-200">
          <div class="px-4 py-5 sm:px-6">
            <h2 class="text-lg font-medium leading-6 text-gray-900">Nabídky obchodů pro rozměr <%= @variant.name %></h2>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Vyberte si z nabídek e-shopů</p>
          </div>

          <div class="border-t border-gray-200">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Obchod</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Dostupnost</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Cena</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Akce</span>
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <% @product.prices.by_variant(@variant).each do |product_price| %>
                  <tr class="hover:bg-gray-50">
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                      <div class="flex items-center">
                        <div class="flex-shrink-0">
                          <img class="h-10" src="<%= asset_path "sellers/pneuboss.svg" %>" alt="">
                        </div>
                      </div>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <span class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                        Skladem ≥ <%= product_price.stock %> ks
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                      <div class="text-red-600 font-medium text-lg">
                        <%= price product_price.price_with_shipping %>
                      </div>
                      <div class="text-gray-500 text-xs">
                        Cena bez dopravy: <%= price product_price.price %>
                      </div>
                    </td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <a href="<%= redirect_to_shop_path(product_price.id) %>" target="_blank" class="btn btn-primary btn-sm">
                        Do obchodu
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4 ml-1">
                          <path fill-rule="evenodd" d="M5.22 14.78a.75.75 0 001.06 0l7.22-7.22v5.69a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75h-7.5a.75.75 0 000 1.5h5.69l-7.22 7.22a.75.75 0 000 1.06z" clip-rule="evenodd" />
                        </svg>
                      </a>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>

        <div class="bg-white mt-6" data-controller="tabs" data-tabs-active-tab-class="border-b-2 border-primary text-primary font-medium">
          <!-- Taby -->
          <div class="border-b border-gray-200">
            <ul class="flex -mb-px space-x-8">
              <li data-tabs-target="tab" data-action="click->tabs#change" class="py-4 px-1 cursor-pointer">
                <a class="text-sm font-medium text-gray-500 hover:text-gray-700 whitespace-nowrap">Popis</a>
              </li>
              <li data-tabs-target="tab" data-action="click->tabs#change" class="py-4 px-1 cursor-pointer">
                <a class="text-sm font-medium text-gray-500 hover:text-gray-700 whitespace-nowrap">Technické specifikace</a>
              </li>
            </ul>
          </div>

          <!-- Panel s popisem -->
          <div data-tabs-target="panel" class="py-4">
            <div class="prose max-w-none text-gray-700">
              <%= @variant.description %>
            </div>
          </div>

          <!-- Panel s technickými specifikacemi -->
          <div data-tabs-target="panel" class="py-4 hidden">
            <div class="overflow-hidden border border-gray-200 rounded-lg">
              <table class="min-w-full divide-y divide-gray-200">
                <tbody class="divide-y divide-gray-200 bg-white">
                  <!-- Základní rozměry -->
                  <tr class="bg-gray-50">
                    <th colspan="2" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rozměry pneumatiky</th>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 w-1/2">Šířka</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.width %> mm</td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Profil</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.profile %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Průměr</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.diameter %>"</td>
                  </tr>

                  <!-- Indexy -->
                  <tr class="bg-gray-50">
                    <th colspan="2" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Indexy</th>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Index rychlosti</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.speed_index %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Index nosnosti</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.weight_index %></td>
                  </tr>

                  <!-- EU štítek -->
                  <tr class="bg-gray-50">
                    <th colspan="2" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EU štítek</th>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Přilnavost za mokra</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.wet_grip.present? ? @variant.wet_grip : 'Neuvedeno' %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Palivová účinnost</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.fuel_efficiency.present? ? @variant.fuel_efficiency : 'Neuvedeno' %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Hlučnost</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.noise.present? ? "#{@variant.noise} dB" : 'Neuvedeno' %></td>
                  </tr>

                  <!-- Další vlastnosti -->
                  <tr class="bg-gray-50">
                    <th colspan="2" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Další vlastnosti</th>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Období</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= t("products.seasons.#{@product.season}") %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">Kategorie</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @product.category.name %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">M+S (Mud and Snow)</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.mud_and_snow ? 'Ano' : 'Ne' %></td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">3PMSF (Three-Peak Mountain Snowflake)</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><%= @variant.three_peak_mountain_snowflake ? 'Ano' : 'Ne' %></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
