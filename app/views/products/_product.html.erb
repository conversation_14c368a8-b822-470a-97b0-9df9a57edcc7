<% if variant.nil? %>
  <% variant = product.variants.first %>
<% end %>
<div class="bg-white group relative border-b border-r border-gray-200 p-4 sm:p-6">
  <a href="<%= product_variant_path(manufacturer_slug: product.manufacturer.slug, product_slug: product.slug, variant_slug: variant.slug) %>" class="overflow-hidden group-hover:opacity-90">
    <% if product.image.attached? %>
      <%= image_tag product.image.variant(resize_to_fill: [250, 230, crop: :low]) %>
    <% else %>
      <div class="bg-gray-100 h-[230px] flex items-center justify-center">
        <span class="text-gray-400">Bez obrázku</span>
      </div>
    <% end %>

    <div class="absolute top-0 left-0 m-3">
      <%= badge(product.season) %>
    </div>
  </a>
  <div class="pb-4 pt-5 text-center">
    <div class="flex flex-col">
      <a href="<%= "/pneumatiky/#{product.manufacturer.slug}" %>" class="text-xs font-bold">
        <%= product.manufacturer.name %>
      </a>

      <a href="<%= "/pneumatiky/#{product.manufacturer.slug}/#{product.slug}" %>" class="font-medium text-sm">
        <%= product.name %>
      </a>

      <% if product.variants.any? %>
        <span class="text-xs text-gray-500 mt-1">
          <%= product.variants.count %> variant<%= product.variants.count == 1 ? 'a' : 'y' %>
        </span>
      <% end %>
    </div>

    <% if product.variants.any? && product.variants.first.cheapest %>
      <div class="mt-3">
        <span class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
          Skladem
        </span>
      </div>

      <div class="mt-3">
        <p class="text-sm font-medium text-gray-900">
          od <span class="text-red-600"><%= price product.variants.first.cheapest.price %></span>
        </p>
      </div>
    <% end %>
  </div>
</div>
