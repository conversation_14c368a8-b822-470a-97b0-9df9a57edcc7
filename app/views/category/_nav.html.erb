
<% nodes.each do |page, children| %>
  <li class="">
    <%= link_to page.name, category_path(page), class: "#{page.slug == 'vanoce' ? 'bg-primary-500 text-white' : ''} block py-2 pl-2 hover:text-black hover:bg-gray-50 #{@category == page ? 'bg-eshop-50' : ''}" %>

    <% if @category && @category.root.id === page.id %>
      <ul class="category-node level-<%= page.depth + 1 %>">
        <%= render 'category/nav', :nodes => page.children.order(priority: :desc) %>
      </ul>
    <% end %>
  </li>
<% end %>