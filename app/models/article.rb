# frozen_string_literal: true

# == Schema Information
#
# Table name: articles
#
#  id             :bigint           not null, primary key
#  backup_content :text
#  content        :text
#  display_ads    :boolean          default(TRUE)
#  other_authors  :string
#  perex          :string
#  published_at   :datetime
#  slug           :string
#  species        :string
#  title          :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  author_id      :bigint           not null
#  magazine_id    :bigint
#  original_id    :integer
#
# Indexes
#
#  index_articles_on_author_id    (author_id)
#  index_articles_on_magazine_id  (magazine_id)
#  index_articles_on_slug         (slug) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (author_id => authors.id)
#  fk_rails_...  (magazine_id => magazines.id)
#
class Article < ApplicationRecord
  include MeiliSearch::Rails

  extend FriendlyId
  friendly_id :title, use: :slugged

  scope :in_category, ->(category) { joins(:categories).where(article_categories: { id: category }) }

  has_one_attached :cover do |attachable|
    attachable.variant :preview, resize_to_fill: [ 315, 200 ], saver: { quality: 100 }, convert: :webp, format: :webp
  end

  has_one_attached :banner

  belongs_to :author
  belongs_to :magazine, optional: true

  has_many :articles_categories, class_name: Article::ArticlesCategories.to_s
  has_many :categories, through: :articles_categories

  accepts_nested_attributes_for :categories

  validates :title, :content, :perex, :published_at, :cover, :categories, presence: true

  meilisearch do
    searchable_attributes %i[title content perex author]

    attribute :title
    attribute :perex
    attribute :content do
      ActionView::Base.full_sanitizer.sanitize(content)
    end

    attribute :author do
      author.name
    end

    attributes_to_highlight [ "*" ]
    attributes_to_crop %i[content content]
    crop_length 10
  end
end
