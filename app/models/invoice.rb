# == Schema Information
#
# Table name: invoices
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  charge_id          :bigint           not null
#  idoklad_invoice_id :integer
#  user_id            :integer
#
# Indexes
#
#  index_invoices_on_charge_id  (charge_id)
#
# Foreign Keys
#
#  fk_rails_...  (charge_id => pay_charges.id)
#
class Invoice < ApplicationRecord
  belongs_to :charge, class_name: "Pay::Charge"
  belongs_to :user

  after_create :create_idoklad_invoice

  def items
    charge.data['line_items'].map do |item|
      {
        Name: item['description'],
        Amount: item['quantity'],
        UnitPrice: charge.amount / 100.0,
        PriceType: 0,
        VatRateType: 0,
        IsTaxMovement: true,
        DiscountPercentage: 0
      }
    end
  end

  def billing_name
    user.full_name
  end

  def currency_id
    case charge.currency
    when "eur"
      2
    when "usd"
      11
    else
      nil # nebo mů<PERSON><PERSON> vr<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> hodnotu
    end
  end


  def create_idoklad_invoice
    return idoklad_invoice_id if idoklad_invoice_id

    date = charge.created_at.strftime("%Y-%m-%d")

    invoice_description = "AwiParrots subscription"

    idoklad = ::Idoklad::Client.new

    invoice_id = idoklad.create_invoice(items, billing_name, user.email, user.first_name, user.last_name, currency_id, date, date, date, invoice_description)

    idoklad.pay(date, invoice_id, charge.amount / 100.0)

    return unless invoice_id

    update(idoklad_invoice_id: invoice_id)
  end
end
