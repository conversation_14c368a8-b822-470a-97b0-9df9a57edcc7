# == Schema Information
#
# Table name: merchants
#
#  id                 :bigint           not null, primary key
#  affiliate_data     :jsonb
#  affiliate_id       :string
#  affiliate_network  :string
#  apify_run_task_url :string
#  email              :string
#  name               :string
#  process_at         :datetime
#  processed_at       :datetime
#  slug               :string
#  url                :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
class Merchant < ApplicationRecord
  has_many :product_prices, dependent: :destroy
  has_many :redirections, dependent: :destroy
  has_many :merchant_pages, dependent: :destroy
  has_many :merchant_page_process_reports, dependent: :destroy

  has_one_attached :logo

  validates :name, presence: true
  validates :slug, presence: true, uniqueness: true, allow_blank: true

  before_validation :set_slug, if: -> { name_changed? || slug.blank? }

  # Typy affiliate sítí
  AFFILIATE_NETWORKS = {
    'none' => 'Žádná',
    'dognet' => 'Dognet',
    'cj' => 'Commission Junction',
    'awin' => 'Awin',
    'custom' => 'Vlastní'
  }.freeze

  def process
    update(processed_at: Time.current, process_at: Time.current + 1.day)
  end

  # Metoda pro spuštění synchronizace cen pro všechny stránky obchodníka
  def synchronize_prices
    return false unless apify_run_task_url.present?
    return false if merchant_pages.empty?

    # Vytvoření jednoho reportu pro všechny stránky
    report = MerchantPageProcessReport.create!(
      merchant: self,
      merchant_page: merchant_pages.first, # Přiřadíme první stránku jako referenční
      started_at: Time.current,
      status: MerchantPageProcessReport::STATUSES[:processing]
    )

    # Označení všech stránek jako synchronizované
    merchant_pages.each do |page|
      page.mark_as_syncing
    end

    # Spuštění Apify tasku pro všechny stránky najednou
    PriceSynchronizationService.run_apify_task_for_merchant(self, report.id)

    true
  end

  def create_redirection_link(product_price)
    case affiliate_network
    when 'dognet'
      create_redirection_link_dognet(product_price)
    when 'cj'
      create_redirection_link_cj(product_price)
    when 'awin'
      create_redirection_link_awin(product_price)
    when 'custom'
      create_redirection_link_custom(product_price)
    else
      product_price.url
    end
  end

  private

  def set_slug
    self.slug = name.parameterize if name.present?
  end

  def create_redirection_link_dognet(product_price)
    return product_price.url unless affiliate_id.present?

    base_url = "https://www.dognet.cz/click"
    params = {
      campaign: affiliate_id,
      deep_url: product_price.url
    }

    "#{base_url}?#{params.to_query}"
  end

  def create_redirection_link_cj(product_price)
    return product_price.url unless affiliate_id.present?

    base_url = "https://www.anrdoezrs.net/click"
    params = {
      pid: affiliate_id,
      url: product_price.url
    }

    "#{base_url}?#{params.to_query}"
  end

  def create_redirection_link_awin(product_price)
    return product_price.url unless affiliate_id.present?

    base_url = "https://www.awin1.com/cread.php"
    params = {
      awinmid: affiliate_id,
      awinaffid: affiliate_data&.dig('affiliate_id') || '',
      clickref: "srovnejpneu",
      p: product_price.url
    }

    "#{base_url}?#{params.to_query}"
  end

  def create_redirection_link_custom(product_price)
    return product_price.url unless affiliate_data.present? && affiliate_data['url_template'].present?

    template = affiliate_data['url_template']
    template.gsub('{url}', CGI.escape(product_price.url))
            .gsub('{affiliate_id}', affiliate_id.to_s)
  end
end
