# frozen_string_literal: true

# == Schema Information
#
# Table name: products
#
#  id                       :bigint           not null, primary key
#  author                   :string
#  binding                  :string
#  count_of_pages           :integer
#  cover_info               :string
#  ean                      :string
#  format                   :string
#  gift_certificate         :boolean          default(FALSE)
#  google_merchant_category :string
#  homepage                 :boolean
#  isbn                     :string
#  issn                     :string
#  name                     :string
#  old_price                :decimal(11, 2)
#  price                    :decimal(11, 2)
#  priority                 :integer
#  released_at              :datetime
#  slug                     :string
#  stock_count              :integer          default(0)
#  tax                      :integer
#  weight                   :integer
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  idoklad_price_id         :integer
#  magazine_id              :bigint
#  original_id              :integer
#
# Indexes
#
#  index_products_on_magazine_id  (magazine_id)
#  index_products_on_slug         (slug) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (magazine_id => magazines.id)
#
class Product < ApplicationRecord
  include MeiliSearch::Rails
  extend FriendlyId
  friendly_id :name, use: :slugged

  validates_presence_of :name, :price, :released_at, :tax

  has_one_attached :cover do |attachable|
    attachable.variant :thumb, resize_to_fit: [ 60, nil ]
    attachable.variant :admin, resize_to_fill: [ 50, 70 ], saver: { quality: 40 }
    attachable.variant :cover, resize_to_fit: [ 200, 280 ], saver: { quality: 100 }, convert: :webp, format: :webp
    attachable.variant :medium, resize_to_fit: [ 250, nil ], saver: { quality: 100 }
  end

  has_and_belongs_to_many :categories

  belongs_to :magazine, optional: true
  after_touch :index!

  has_one_attached :preview

  has_rich_text :perex
  has_rich_text :description

  scope :with_category, ->(category) { joins(:categories).where(categories: { id: category }) }
  scope :with_categories, ->(categories) { joins(:categories).where(categories: { id: categories }) }
  scope :meilisearch_import, -> { includes(cover_attachment: [ :blob ]) }

  def preorder?
    released_at && released_at > Time.now
  end

  def available?
    stock_count >= 1
  end

  def attachment
    preview
  end

  meilisearch do
    searchable_attributes %i[name description perex]
    filterable_attributes %i[species]

    attribute :name
    attribute :perex do
      perex.body&.to_plain_text
    end

    attribute :articles do
      magazine_contents = []
      if magazine.present?
        magazine.contents.each do |content|
          magazine_contents << { article: content.article, author: content.author, species: content.species, page: content.page }
        end
      end

      magazine_contents
    end

    attribute :description do
      if magazine.present?
        magazine.contents.map { |content| "#{content.article} #{content.author} #{content.topic} #{content.species}"  }.join("\n")
      else
        description.body&.to_plain_text
      end
    end

    attribute :species do
      magazine.contents.map { |item| item.species.split(" ")  }.reject(&:empty?).flatten.map(&:parameterize) if magazine.present?
    end

    attributes_to_highlight [ "*" ]
    attributes_to_crop %i[description perex]
    crop_length 10
  end
end
