# frozen_string_literal: true

module Ilovepdf
  class Client
    def initialize(file)
      @file = file
      @ilovepdf = ::Ilovepdf::Ilovepdf.new(
        "project_public_0f135ec17f5b498d19fe5ccbfa327924_u7gg946ebc81a5fd5cd44ca93caf0ddb7fbc8",
        "secret_key_99b90943d425c8273b317f284cce0a75_80dR_15a7c6407003e830705a140e652337be"
      )
    end

    def create_preview_pdf(ranges)
      task = @ilovepdf.new_task :split
      task.add_file @file

      task.ranges = ranges
      task.merge_after = true
      task.execute
      task.download

      task.download_info.output_file
    end
  end
end
