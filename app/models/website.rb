# == Schema Information
#
# Table name: websites
#
#  id                :bigint           not null, primary key
#  address           :string
#  available_locales :jsonb
#  city              :string
#  country           :string
#  data              :jsonb
#  domain            :string
#  email             :string
#  locale            :string           default("cs")
#  map_url           :string
#  name              :string
#  phone             :string
#  postal_code       :string
#  social_networks   :jsonb
#  theme             :jsonb
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :bigint           not null
#
# Indexes
#
#  index_websites_on_account_id  (account_id)
#
# Foreign Keys
#
#  fk_rails_...  (account_id => accounts.id)
#
class Website < ApplicationRecord
  AVAILABLE_LOCALES = %w[cs en de pl].freeze
  LOCALE_NAMES = { cs: "Češ<PERSON>", en: "English", de: "Deutsch", pl: "Polski" }.freeze

  LOGO_SIZES = {
    "64px" => "max-w-[64px]",
    "96px" => "max-w-[96px]",
    "128px" => "max-w-[128px]",
    "160px" => "max-w-[160px]",
    "192px" => "max-w-[192px]",
    "256px" => "max-w-[256px]"
  }.freeze

  HEADER_TYPES = {
    "header" => "Standardní hlavička",
    "header2" => "Hlavička s centrovaným logem"
  }.freeze

  store_accessor :available_locales
  store_accessor :data, :opening_hours_text, :logo_size, :header_type
  store_accessor :social_networks, :facebook, :instagram, :x, :youtube
  store_accessor :theme, :template_key, :font_family, :border_radius, :button_style,
                 :soft_base, :soft_base_content, :soft_accent, :soft_accent_content,
                 :light_base, :light_base_content, :light_accent, :light_accent_content,
                 :neutral_base, :neutral_base_content, :neutral_accent, :neutral_accent_content,
                 :primary_base, :primary_base_content, :primary_accent, :primary_accent_content

  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :phone, presence: true, format: { with: /\A\+?[0-9\s]+\z/ }

  normalizes :email, :phone, :name, with: ->(attribute) { attribute.strip }

  belongs_to :account

  has_one_attached :logo do |attachable|
    attachable.variant :resized, resize_to_limit: [ 150, 150 ], saver: { quality: 100 }
  end

  has_many :pages, dependent: :destroy
  has_many :pricing, dependent: :destroy
  has_many :reviews, dependent: :destroy
  has_many :opening_hours, dependent: :destroy
  has_many :forms, dependent: :destroy
  has_many :inbox_messages, dependent: :destroy

  accepts_nested_attributes_for :opening_hours, allow_destroy: true
  validates_associated :opening_hours

  before_save :merge_locales

  def merge_locales
    self.available_locales = ([ locale ] + Array.wrap(available_locales)).uniq
  end

  def custom_opening_hours
    opening_hours.reject do |opening_hour|
      opening_hour.date.nil? || opening_hour.date.end_of_day < Time.now || OpeningHour.holiday_dates[Time.now.year.to_s].include?(opening_hour.date.to_date.to_s)
    end
  end

  def sms_on_reservation_confirm?
    sms_on_reservation_confirm == "1"
  end

  def sms_on_reservation_cancel?
    sms_on_reservation_cancel == "1"
  end

  def logo_size_class
    LOGO_SIZES[logo_size] || LOGO_SIZES["128px"]
  end

  def header_partial
    header_type.present? ? "shared/#{header_type}" : "shared/header"
  end

  def available_templates
    Rails.application.config.x.themes
  end

  def current_template
    return available_templates[:default] unless template_key.present?
    available_templates[template_key.to_sym] || available_templates[:default]
  end

  def available_color_themes
    current_template.dig(:colors) || {}
  end

  def theme_colors_with_defaults
    template = current_template
    result = {}

    available_color_themes.each do |theme_name, theme_data|
      next unless theme_data.is_a?(Hash) && theme_data.key?(:base)

      result[theme_name] = {
        base: send("#{theme_name}_base").presence || theme_data[:base],
        base_content: send("#{theme_name}_base_content").presence || theme_data[:base_content],
        accent: send("#{theme_name}_accent").presence || theme_data[:accent],
        accent_content: send("#{theme_name}_accent_content").presence || theme_data[:accent_content]
      }
    end

    result
  end

  def template_settings_with_defaults
    template = current_template
    {
      font_family: font_family.presence || 'Inter, system-ui, sans-serif',
      border_radius: border_radius.presence || template.dig(:radius, :field) || '0.5rem',
      button_style: button_style.presence || 'solid',
      navigation: template[:navigation],
      radius: template[:radius],
      sizes: template[:sizes],
      options: template[:options]
    }
  end

  def load_template!(template_key)
    template = available_templates[template_key.to_sym]
    return false unless template

    self.template_key = template_key

    # Načti všechna témata z šablony
    template.dig(:colors)&.each do |theme_name, theme_data|
      next unless theme_data.is_a?(Hash) && theme_data.key?(:base)

      send("#{theme_name}_base=", theme_data[:base])
      send("#{theme_name}_base_content=", theme_data[:base_content])
      send("#{theme_name}_accent=", theme_data[:accent])
      send("#{theme_name}_accent_content=", theme_data[:accent_content])
    end

    # Načti ostatní nastavení
    self.border_radius = template.dig(:radius, :field) || '0.5rem'
    true
  end
end
