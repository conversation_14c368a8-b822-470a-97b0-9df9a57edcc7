# frozen_string_literal: true

module GoSms
  class Client
    CLIENT_ID = "15001_5bq1dict1sgs8kocgsog40sg0kksw8gs0ks48oggsskcwc0g04"
    CLIENT_SECRET = "6694mywyqv8ko88048k88sowkskocw084swgckccsgs0ckkosc"
    API_URL = "https://app.gosms.eu/api/v1/"
    AUTH_URL = "https://app.gosms.eu/oauth/v2/"

    def request_access_token
      conn = Faraday.new(url: AUTH_URL) do |f|
        f.request :url_encoded
      end

      response = conn.post("token") do |req|
        req.body = { grant_type: "client_credentials", client_id: CLIENT_ID, client_secret: CLIENT_SECRET }
      end

      JSON.parse(response.body)["access_token"]
    end

    def send_sms(message, recipient)
      conn = Faraday.new(url: API_URL, params: { access_token: request_access_token }) do |f|
        f.request :url_encoded
      end

      conn.post("messages") do |req|
        req.body = { message:, recipients: [ recipient ], channel: "310038" }.to_json
      end
    end
  end
end
