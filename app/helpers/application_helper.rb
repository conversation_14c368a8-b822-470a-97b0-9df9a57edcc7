module ApplicationHelper
  def admin?
    controller.present? && controller.class.name.split("::").first=="Admin"
  end

  def page_path_resolver(page)
    return "#" if controller.present? && admin?
    return page.link if page.is_link?
    return homepage_path if page.is_homepage

    if page.is_anchor? && page.anchor_block.present?
      return "#block-#{page.anchor_block.id}" if page.is_anchor? && page.anchor_block.present?
      return "/#block-#{page.anchor_block.id}" if @page != page
    end

    locale = I18n.locale || :cs

    if I18n.locale == Current.website.locale.to_sym
      locale = nil
    end

    page_path(page, locale:)
  end

  def pages
    @pages ||= Page.published.where(locale: I18n.locale).where.not(scope: nil).order(:position).arrange
  end

  def pages_left
    all_pages = pages.to_a
    half_count = (all_pages.length / 2.0).ceil
    all_pages.first(half_count).to_h
  end

  def pages_right
    all_pages = pages.to_a
    half_count = (all_pages.length / 2.0).ceil
    all_pages.last(all_pages.length - half_count).to_h
  end

  def render_header
    render Current.website.header_partial, page_nodes: pages
  end

  def display_logo(logo)
    return current_tenant.name unless logo.attached?

    image = logo.variable? ? logo.variant(:resized) : logo

    image_tag image, class: "w-auto object-cover"
  end

  def show_svg(path)
    File.open("app/assets/images/#{path}", "rb") do |file|
      raw file.read
    end
  end

  def header_element(&block)
    #  stimulus_controller = current_tenant.theme.nav_position == "fixed" ? "sticky-header" : nil

    #extra_padding = Current.website&.header_type == "header2" ? "padding-top: 32px;" : ""

    content_tag :header,
                class: "#{@theme[:navigation] unless admin?} top-0 left-0 w-full z-[500]",
                data: { theme: "white" },
                &block
  end

  def pricing
    @pricing ||= Current.website.pricing
  end

  def day_name(date)
    I18n.t("date.day_names")[date.strftime("%w").to_i]
  end
  def weekdays
    [
      { label: "Pondělí", value: 1 },
      { label: "Úterý", value: 2 },
      { label: "Středa", value: 3 },
      { label: "Čtvrtek", value: 4 },
      { label: "Pátek", value: 5 },
      { label: "Sobota", value: 6 },
      { label: "Neděle", value: 0 }
    ]
  end

  def error_message(message)
    content_tag(:div, class: "bg-orange-50 px-3 py-1.5") do
      content_tag(:div, class: "flex") do
        content_tag(:div, class: "flex-shrink-0") do
        end +
          content_tag(:div, class: "flex-1 md:flex md:justify-between") do
            content_tag(:p, message, class: "text-sm text-orange-900")
          end
      end
    end
  end

  def price_formatted(dish)
    price = dish.price

    if I18n.locale == :cs
      result = number_to_currency(price, unit: "CZK", separator: ",", delimiter: " ")

      result&.sub(",00", "")
    else
      "#{dish.price_eur} €"
    end
  end

  def generate_color_variables_from_yaml(data)
    css_lines = []
    data.each do |theme, values|
      values.each do |key, value|
        css_lines << "--color-#{theme}-#{key.to_s.dasherize}: #{value};"
      end
    end
    css_lines.join("\n")
  end

  def generate_theme_css_variables
    return "" unless current_tenant&.theme&.any?

    theme_colors = current_tenant.theme_colors_with_defaults
    template_settings = current_tenant.template_settings_with_defaults
    css_lines = []

    # Generuj CSS proměnné pro všechna barevná témata
    theme_colors.each do |theme_name, colors|
      css_lines << "--color-#{theme_name.to_s.dasherize}-base: #{colors[:base]};" if colors[:base]
      css_lines << "--color-#{theme_name.to_s.dasherize}-base-content: #{colors[:base_content]};" if colors[:base_content]
      css_lines << "--color-#{theme_name.to_s.dasherize}-accent: #{colors[:accent]};" if colors[:accent]
      css_lines << "--color-#{theme_name.to_s.dasherize}-accent-content: #{colors[:accent_content]};" if colors[:accent_content]
    end

    # Obecná nastavení
    css_lines << "--theme-font-family: #{template_settings[:font_family]};" if template_settings[:font_family]
    css_lines << "--theme-border-radius: #{template_settings[:border_radius]};" if template_settings[:border_radius]
    css_lines << "--theme-button-style: #{template_settings[:button_style]};" if template_settings[:button_style]

    css_lines.join("\n            ")
  end

  def google_fonts_link
    return "" unless current_tenant&.theme&.any?

    font_family = current_tenant.font_family
    return "" if font_family.blank?

    # Mapování fontů na Google Fonts názvy
    google_fonts_map = {
      'Roboto, sans-serif' => 'Roboto:wght@300;400;500;700',
      'Open Sans, sans-serif' => 'Open+Sans:wght@300;400;500;600;700',
      'Lato, sans-serif' => 'Lato:wght@300;400;700',
      'Poppins, sans-serif' => 'Poppins:wght@300;400;500;600;700',
      'Montserrat, sans-serif' => 'Montserrat:wght@300;400;500;600;700',
      'Source Sans Pro, sans-serif' => 'Source+Sans+Pro:wght@300;400;600;700'
    }

    google_font = google_fonts_map[font_family]
    return "" unless google_font

    "<link href=\"https://fonts.googleapis.com/css2?family=#{google_font}&display=swap\" rel=\"stylesheet\">".html_safe
  end
end
