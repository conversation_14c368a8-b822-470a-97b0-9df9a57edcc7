class Admin::MagazinesController < Admin::AdminController
  def index
    @magazines = Magazine.all.order(year: :desc, number: :desc)
  end

  def new
    @magazine = Magazine.new
    16.times { @magazine.contents.build }
  end

  def edit
    @magazine = Magazine.find(params[:id])
    3.times { @magazine.contents.build }
  end

  def create
    @magazine = Magazine.new(magazine_params)

    if @magazine.save
      redirect_to edit_admin_magazine_path(@magazine), notice: "<PERSON><PERSON>ženo!"
    else
      render "edit"
    end
  end

  def update
    @magazine = Magazine.find(params[:id])

    if @magazine.update(magazine_params)
      redirect_to edit_admin_magazine_path(@magazine), notice: "<PERSON>loženo!"
    else
      render :edit
    end
  end

  private

  def magazine_params
    params.require(:magazine).permit(:number, :year, :cover, :released_at, :flipbook_url, :flipbook_preview_url, :download_url, contents_attributes: [:id, :article, :author, :page, :species, :topic])
  end
end
