class Admin::MerchantPagesController < ApplicationController
  layout 'admin'
  include Pagy::Backend

  before_action :set_merchant, only: [:index, :new, :create]
  before_action :set_merchant_page, only: [:destroy, :sync]

  def index
    @pagy, @merchant_pages = pagy(@merchant.merchant_pages.order(created_at: :desc), items: 20)
  end

  def new
    @merchant_page = @merchant.merchant_pages.new
  end

  def create
    @merchant_page = @merchant.merchant_pages.new(merchant_page_params)

    if @merchant_page.save
      redirect_to admin_merchant_merchant_pages_path(@merchant), notice: 'URL adresa byla úspěšně přidána.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    @merchant_page.destroy

    redirect_to admin_merchant_merchant_pages_path(@merchant_page.merchant), notice: 'URL adresa byla ú<PERSON> s<PERSON>.', status: :see_other
  end

  def sync
    # Spuštění synchronizace stránky obchodníka
    MerchantPageSyncJob.perform_later(@merchant_page)

    redirect_to admin_merchant_merchant_pages_path(@merchant_page.merchant), notice: 'Synchronizace URL adresy byla spušt<PERSON>na.'
  end

  private

  def set_merchant
    @merchant = Merchant.find(params[:merchant_id])
  end

  def set_merchant_page
    @merchant_page = MerchantPage.find(params[:id])
  end

  def merchant_page_params
    params.require(:merchant_page).permit(:url, :name)
  end
end
