module Admin
  class SettingsController < ApplicationController
    layout "admin/settings"
    def show
      add_breadcrumb "Nastavení webu"
    end

    def update
      if current_tenant.update(website_params)
        redirect_to admin_settings_path, notice: "Nastavení by<PERSON>."
      else
        render :show, status: :unprocessable_entity
      end
    end

    private

    def website_params
      params[:website][:available_locales] ||= []
      params.require(:website).permit(:name, :phone, :email, :city, :address, :zip, :locale, :logo, :logo_size, :header_type, available_locales: [])
    end
  end
end
