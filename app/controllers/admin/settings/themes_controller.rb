class Admin::Settings::ThemesController < Admin::ApplicationController
  layout "admin/settings"

  def show
    add_breadcrumb "Nastavení webu", admin_settings_path
    add_breadcrumb "Téma"
  end

  def update
    if current_tenant.update(theme_params)
      redirect_to admin_settings_theme_path, notice: "Nastavení tématu bylo <PERSON>."
    else
      render :show, status: :unprocessable_entity
    end
  end

  def load_preset
    preset_key = params[:preset_key]
    
    if current_tenant.load_theme_preset!(preset_key) && current_tenant.save
      redirect_to admin_settings_theme_path, notice: "Přednastavené téma '#{current_tenant.available_theme_presets[preset_key.to_sym][:name]}' bylo <PERSON> načteno."
    else
      redirect_to admin_settings_theme_path, alert: "Nepodařilo se načíst přednastaven<PERSON> téma."
    end
  end

  private

  def theme_params
    params.require(:website).permit(
      :primary_color, :secondary_color, :accent_color, :base_color,
      :base_content_color, :accent_content_color, :font_family, 
      :border_radius, :button_style, :theme_preset
    )
  end
end
