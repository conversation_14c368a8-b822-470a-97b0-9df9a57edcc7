class Admin::PagesController < ApplicationController
  layout 'admin'
  before_action :set_page, only: [:show, :edit, :update, :destroy]

  def index
    @pages = Page.all.order(created_at: :desc)
  end

  def show
  end

  def new
    @page = Page.new
  end

  def edit
  end

  def create
    @page = Page.new(page_params)

    if @page.save
      redirect_to admin_page_path(@page), notice: '<PERSON>ránka byla úspěšně vytvořena.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @page.update(page_params)
      redirect_to admin_page_path(@page), notice: '<PERSON>r<PERSON>ka byla úspěšně aktualizována.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @page.destroy
    redirect_to admin_pages_path, notice: 'Stránka byla úspěšně s<PERSON>zána.'
  end

  private

  def set_page
    @page = Page.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    # Pokud nenajdeme podle ID, zkusíme najít podle slugu
    @page = Page.find_by_slug!(params[:id])
  end

  def page_params
    params.require(:page).permit(:title, :slug, :published, :content)
  end
end
