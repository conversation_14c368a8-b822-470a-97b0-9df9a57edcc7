class Admin::ArticlesController < Admin::AdminController
  def index
    @articles = Article.order(created_at: :desc)
  end

  def new
    @article = Article.new
  end

  def edit
    @article = Article.friendly.find(params[:id])
  end

  def create
    @article = Article.new(article_params)

    if @article.save
      redirect_to admin_article_path(@article), notice: "Uloženo!"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @article = Article.friendly.find(params[:id])

    if @article.update(article_params)
      redirect_to admin_article_path(@article), notice: "Uloženo!"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def article_params
    params.require(:article).permit(:title, :perex, :content, :cover, :published_at, :user_id, :cover, :slider, category_ids: [])
  end
end