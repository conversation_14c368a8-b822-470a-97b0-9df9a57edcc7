class PagesController < ApplicationController
  before_action :set_page, only: %i[show homepage]
  before_action :set_meta_tags_for_page, only: %i[show homepage]

  def show
    if @page
      redirect_to @page.link, allow_other_host: true if @page.is_link?
      redirect_to homepage_path(anchor: @page.anchor) if @page.is_anchor?

      @blocks = @page.blocks.visible if @page
    else
      render_404
    end
  end

  def homepage
    # instagram_collection = Media.limit(20)
    @instagram_media = Media.where.not(published_at: nil).order(published_at: :desc).limit(10)
    render_404 unless @page
  end

  private

  def set_page
    @page = if action_name == "show"
              Page.includes(blocks: [:controls])
                  .find_by(slug: params[:slug], locale: locale)
            else
              Page.includes(blocks: [:controls])
                  .find_by(is_homepage: true, locale: locale)
            end
  end

  def set_meta_tags_for_page
    return unless @page

    set_meta_tags(
      title: @page.meta_title || @page.title,
      description: @page.meta_description || nil
    )
  end

  def render_404
    render file: Rails.root.join("public", "404.html"), layout: false, status: :not_found
  end
end
