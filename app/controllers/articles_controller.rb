class ArticlesController < ApplicationController
  before_action :set_categories, :set_count_of_articles

  def index
    @category = params[:id] ? ::Article::Category.friendly.find(params[:id]) : nil

    articles = Article.published.newest
    articles = articles.in_category(@category) if @category

    @pagy, @articles = pagy(articles, items: 36)
  end

  def show
    @article = Article.friendly.find(params[:id])
  end

  def search
    redirect_to articles_path if params[:q].empty?
  end

  private

  def set_categories
    @categories = Article::Category.all
  end

  def set_count_of_articles
    @count_of_articles = Article.published.count
  end
end