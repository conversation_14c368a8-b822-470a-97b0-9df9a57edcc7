# frozen_string_literal: true

class ConversationsController < ApplicationController
  def update
    conversation = Conversation.find(params[:id])

    conversation.messages.build(content: params[:conversation][:content], user: current_user,
                                images: params[:conversation][:images])

    return unless conversation.save

    redirect_to account_conversation_path(conversation)
  end
end
