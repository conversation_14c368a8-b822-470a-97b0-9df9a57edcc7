require 'uri'
require 'net/http'
require 'openssl'

class OrdersController < ApplicationController
  before_action :initialize_values

  def show
    @order = GlobalID::Locator.locate_signed(params[:id], for: :payment)
  end

  def new
    if user_signed_in?
      redirect_to root_page
    end

    console
    @order = Order.new
  end

  def create

  end

  def stripe

  end

  def paypal

  end

  def cancel
    @order = GlobalID::Locator.locate_signed(params[:id], for: :payment)

    @order.update(canceled_at: Time.now)
    redirect_to root_path, notice: "Order was cancelled"
  end

  private

  def order_params
    params.require(:order).permit(:currency, :plan_id)
  end

  def user_params
    params.require(:order).permit(user: [:email, :country, :first_name, :last_name, :currency])
  end

  def initialize_values
    @user = User.new
    @plans = Plan.where(voucher_code: nil)
    @magazine = Magazine.current_issue
  end
end
