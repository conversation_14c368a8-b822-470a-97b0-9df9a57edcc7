/*
 * Trix Editor Overrides
 * <PERSON>to styly p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> styly Trix editoru, aby sp<PERSON><PERSON><PERSON><PERSON><PERSON> fungovaly s Tailwind CSS
 */

/* Základní styly pro editor */
trix-editor {
  min-height: 20rem;
  max-height: 40rem;
  overflow-y: auto;
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.375rem; /* rounded-md */
  padding: 0.75rem;
  color: #1f2937; /* gray-800 */
}

trix-editor:focus {
  outline: 2px solid #10b981; /* emerald-500 */
  outline-offset: 2px;
}

/* Styly pro toolbar */
trix-toolbar {
  border-bottom: 1px solid #e5e7eb; /* gray-200 */
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

trix-toolbar .trix-button-group {
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.25rem; /* rounded */
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

trix-toolbar .trix-button {
  border: none;
  background: white;
  color: #4b5563; /* gray-600 */
}

trix-toolbar .trix-button:hover {
  background-color: #f3f4f6; /* gray-100 */
}

trix-toolbar .trix-button.trix-active {
  background-color: #e5e7eb; /* gray-200 */
  color: #1f2937; /* gray-800 */
}

/* Styly pro obsah */
trix-editor h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

trix-editor ul {
  list-style-type: disc;
  padding-left: 2rem;
  margin: 0.5rem 0;
}

trix-editor ol {
  list-style-type: decimal;
  padding-left: 2rem;
  margin: 0.5rem 0;
}

trix-editor li {
  margin-bottom: 0.25rem;
}

trix-editor a {
  color: #10b981; /* emerald-500 */
  text-decoration: underline;
}

trix-editor blockquote {
  border-left: 3px solid #d1d5db; /* gray-300 */
  padding-left: 1rem;
  color: #4b5563; /* gray-600 */
  font-style: italic;
  margin: 0.5rem 0;
}

/* Styly pro zobrazení obsahu */
.trix-content h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.trix-content ul {
  list-style-type: disc;
  padding-left: 2rem;
  margin: 0.5rem 0;
}

.trix-content ol {
  list-style-type: decimal;
  padding-left: 2rem;
  margin: 0.5rem 0;
}

.trix-content li {
  margin-bottom: 0.25rem;
}

.trix-content a {
  color: #10b981; /* emerald-500 */
  text-decoration: underline;
}

.trix-content blockquote {
  border-left: 3px solid #d1d5db; /* gray-300 */
  padding-left: 1rem;
  color: #4b5563; /* gray-600 */
  font-style: italic;
  margin: 0.5rem 0;
}
