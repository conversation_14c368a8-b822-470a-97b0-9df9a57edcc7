// Test file to check for Safari-specific issues
console.log("Testing for Safari-specific issues");

// Test for problematic string replacements
var testString = "Press enter to add a new text...";
var testString2 = "Drag to upload a new image<br>or click to select";
var testString3 = "Open link in new tab";

// Test for problematic regex
var regex = /^https?\:\/\/(?:www\.youtube(?:\-nocookie)?\.com\/|m\.youtube\.com\/|youtube\.com\/)?(?:ytscreeningroom\?vi?=|youtu\.be\/|vi?\/|user\/.+\/u\/\w{1,2}\/|embed\/|watch\?(?:.*\&)?vi?=|\&vi?=|\?(?:.*\&)?vi?=)([^#\&\?\n\/<>"']*)/gi;

// Test for problematic function definitions
function testFunction() {
    /* This is a multiline comment
       with multiple lines
    */
    return true;
}

// Test for problematic object properties
var testObj = {
    _toggle: function(params, remove) {
        console.log("Toggle function called");
    }
};

console.log("Safari test completed");
