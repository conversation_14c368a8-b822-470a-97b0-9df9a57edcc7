ArticleEditor.add("plugin","blockcode",{translations:{en:{blockcode:{save:"Save",cancel:"Cancel","edit-code":"Edit Code"}}},defaults:{icon:'<svg height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="m10.6128994 3.20970461.0942074.08318861 4 4c.3604839.36048396.3882135.92771502.0831886 1.32000622l-.0831886.09420734-4 4.00000002c-.3905243.3905243-1.02368929.3905243-1.41421358 0-.36048396-.360484-.3882135-.927715-.08318861-1.3200062l.08318861-.0942074 3.29210678-3.2928932-3.29210678-3.29289322c-.36048396-.36048396-.3882135-.92771502-.08318861-1.32000622l.08318861-.09420734c.36048396-.36048396.92771498-.3882135 1.32000618-.08318861zm-3.90579262.08318861c.36048396.36048396.3882135.92771502.08318861 1.32000622l-.08318861.09420734-3.29210678 3.29289322 3.29210678 3.2928932c.36048396.360484.3882135.927715.08318861 1.3200062l-.08318861.0942074c-.36048396.3604839-.92771502.3882135-1.32000622.0831886l-.09420734-.0831886-4-4.00000002c-.36048396-.36048396-.3882135-.92771502-.08318861-1.32000622l.08318861-.09420734 4-4c.39052429-.39052429 1.02368927-.39052429 1.41421356 0z"/></svg>',popup:{width:"100%",title:"## blockcode.edit-code ##",form:{code:{type:"textarea",rows:"8"}},footer:{save:{title:"## blockcode.save ##",command:"blockcode.save",type:"primary"},cancel:{title:"## blockcode.cancel ##",close:!0}}}},init:function(){this.offset=!1},start:function(){this.app.control.add("blockcode",{icon:this.opts.blockcode.icon,command:"blockcode.edit",title:"## blockcode.edit-code ##",blocks:{all:!0,except:["variable"]}})},edit:function(){var t=this.app.block.get(),e=t.getOuterHtml(),e=this.app.parser.unparse(e),t=(t.isEditable()&&(this.offset=this.app.offset.get(t.getBlock())),this.app.popup.create("code",this.opts.blockcode.popup));t.setData({code:e}),this.app.popup.open({focus:"code"}),this._buildInputHandle(t),this._buildCodemirror(t)},save:function(t){this.app.popup.close();var e=this.app.block.get(),t=this._getCode(t);""!==t&&(t=this.app.parser.parse(t,!1),t=this.dom(t),e=this.app.create("block."+e.getType(),t),this.app.block.change(e),this.offset&&e.isEditable()&&this.app.offset.set(e.getBlock(),this.offset),this.offset=!1)},_getCode:function(t){t=t.getData().code.trim();return this.app.codemirror.val(t)},_buildInputHandle:function(t){t.getInput("code").on("keydown",this.app.input.handleTextareaTab.bind(this))},_buildCodemirror:function(t){t=t.getInput("code");this.app.codemirror.create({el:t,height:"200px",focus:!0}),this.app.popup.updatePosition()}});